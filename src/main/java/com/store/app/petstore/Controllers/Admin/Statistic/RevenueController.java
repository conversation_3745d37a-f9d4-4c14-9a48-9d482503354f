package com.store.app.petstore.Controllers.Admin.Statistic;

import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.scene.chart.XYChart;
import javafx.scene.control.*;
import javafx.scene.chart.BarChart;

import com.store.app.petstore.DAO.StatisticDAO.RevenueDAO;
import com.store.app.petstore.Models.Records.StaffRevenueStats;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Map;

public class RevenueController {

    @FXML private Label totalRevenueValueLabel;
    @FXML private DatePicker revenueChartFilter1;
    @FXML private DatePicker revenueChartFilter2;
    @FXML private Label monthlyRevenueLabel;
    @FXML private Label lastMonthRevenueLabel;
    @FXML private Label revenueRatioLabel;
    @FXML private TableView<StaffRevenueStats> staffRevenueTable;
    @FXML private BarChart<String, Number> summaryChart;
    @FXML private Button viewChartButton;
    @FXML private ChoiceBox<String> staffRevenueFilter;
    @FXML private ProgressBar chartProgressBar;
    @FXML private ProgressBar staffTableProgressBar;
    @FXML private ChoiceBox<String> monthChoiceBox;

    // === Initialize ===
    @FXML
    public void initialize() {
        initializeStaffRevenueFilters();
        setupChoiceBoxes();
        initializeDatePickers();
        loadDefaultRevenueStatistics();
        setupChartStyling();
    }

    private void initializeStaffRevenueFilters() {
        if (staffRevenueFilter != null) {
            staffRevenueFilter.setItems(FXCollections.observableArrayList(
                "Tháng 1", "Tháng 2", "Tháng 3", "Tháng 4", "Tháng 5", "Tháng 6",
                "Tháng 7", "Tháng 8", "Tháng 9", "Tháng 10", "Tháng 11", "Tháng 12"
            ));
            staffRevenueFilter.setValue("Tháng " + LocalDate.now().getMonthValue());
        }
    }

    private void setupChoiceBoxes() {
        if (monthChoiceBox != null) {
            monthChoiceBox.setItems(FXCollections.observableArrayList(
                "Tất cả", "Tháng 1", "Tháng 2", "Tháng 3", "Tháng 4", "Tháng 5", "Tháng 6",
                "Tháng 7", "Tháng 8", "Tháng 9", "Tháng 10", "Tháng 11", "Tháng 12"
            ));
            monthChoiceBox.setValue("Tất cả");

            monthChoiceBox.setOnAction(e -> onMonthFilterChanged());
        }
    }

    private void initializeDatePickers() {
        // Set default date range to 1 year (all data)
        LocalDate now = LocalDate.now();
        LocalDate startOfYear = now.withDayOfYear(1);

        revenueChartFilter1.setValue(startOfYear);
        revenueChartFilter2.setValue(now);

        // Add listeners for automatic updates
        revenueChartFilter1.setOnAction(e -> onDateRangeChanged());
        revenueChartFilter2.setOnAction(e -> onDateRangeChanged());
    }

    private void setupChartStyling() {
        // Configure chart appearance
        summaryChart.setAnimated(true);
        summaryChart.setLegendVisible(false);
        summaryChart.setTitle("Doanh thu theo nhân viên");
    }

    private void onDateRangeChanged() {
        if (revenueChartFilter1.getValue() != null && revenueChartFilter2.getValue() != null) {
            if (revenueChartFilter1.getValue().isAfter(revenueChartFilter2.getValue())) {
                showAlert("Lỗi", "Ngày bắt đầu không thể sau ngày kết thúc!");
                return;
            }
            // Auto-refresh chart when date range changes
            handleViewChartButtonAction();
        }
    }

    private void onMonthFilterChanged() {
        String selectedMonth = monthChoiceBox.getValue();
        System.out.println("Month filter changed to: " + selectedMonth);

        if (selectedMonth != null && !selectedMonth.equals("Tất cả")) {
            try {
                int monthNumber = Integer.parseInt(selectedMonth.replace("Tháng ", ""));
                int year = LocalDate.now().getYear();
                loadStaffRevenueChartByMonth(monthNumber, year);

                // Also update table
                LocalDate startDate = LocalDate.of(year, monthNumber, 1);
                LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
                loadStaffRevenueData(startDate, endDate);
            } catch (NumberFormatException e) {
                System.err.println("Error parsing month number from: " + selectedMonth);
            }
        } else {
            // Show all data when "Tất cả" is selected
            handleViewChartButtonAction();
        }
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void loadDefaultRevenueStatistics() {
        LocalDate now = LocalDate.now();
        LocalDate startDate = now.withDayOfYear(1); // Start of current year
        LocalDate endDate = now;

        revenueChartFilter1.setValue(startDate);
        revenueChartFilter2.setValue(endDate);

        fetchAndDisplayRevenueStatistics(startDate, endDate);
    }

    @FXML
    private void handleViewChartButtonAction() {
        LocalDate startDate = revenueChartFilter1.getValue();
        LocalDate endDate = revenueChartFilter2.getValue();

        if (startDate != null && endDate != null) {
            fetchAndDisplayRevenueStatistics(startDate, endDate);
        } else {
            System.out.println("Vui lòng chọn cả ngày bắt đầu và ngày kết thúc.");
        }
    }

    @FXML
    private void handleViewStaffButtonAction() {
        String selectedMonth = staffRevenueFilter.getValue();

        if (selectedMonth == null) return;

        int month = staffRevenueFilter.getItems().indexOf(selectedMonth) + 1;
        int year = LocalDate.now().getYear();

        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());

        loadStaffRevenueData(startDate, endDate);
    }

    private void fetchAndDisplayRevenueStatistics(LocalDate startDate, LocalDate endDate) {
        chartProgressBar.setVisible(true);

        Task<Void> task = new Task<>() {
            @Override
            protected Void call() throws Exception {
                Map<String, String> revenueStats = RevenueDAO.fetchRevenueDataFromDatabase(startDate, endDate);
                ObservableList<XYChart.Data<String, Number>> staffChartData =
                        RevenueDAO.fetchStaffRevenueChartData(startDate, endDate);
                ArrayList<StaffRevenueStats> staffData =
                        RevenueDAO.fetchStaffRevenueDataFromDatabase(startDate, endDate);

                Platform.runLater(() -> {
                    updateRevenueLabels(revenueStats);
                    updateStaffRevenueChart(staffChartData);
                    updateStaffTable(staffData);
                });
                return null;
            }

            @Override
            protected void succeeded() {
                chartProgressBar.setVisible(false);
            }

            @Override
            protected void failed() {
                chartProgressBar.setVisible(false);
                getException().printStackTrace();
            }
        };

        new Thread(task).start();
    }

    private void loadStaffRevenueChartByMonth(int month, int year) {
        chartProgressBar.setVisible(true);

        Task<ObservableList<XYChart.Data<String, Number>>> task = new Task<>() {
            @Override
            protected ObservableList<XYChart.Data<String, Number>> call() throws Exception {
                return RevenueDAO.fetchStaffRevenueChartDataByMonth(month, year);
            }

            @Override
            protected void succeeded() {
                updateStaffRevenueChart(getValue());
                chartProgressBar.setVisible(false);
            }

            @Override
            protected void failed() {
                chartProgressBar.setVisible(false);
                getException().printStackTrace();
            }
        };

        new Thread(task).start();
    }

    private void loadStaffRevenueData(LocalDate startDate, LocalDate endDate) {
        staffTableProgressBar.setVisible(true);

        Task<ObservableList<StaffRevenueStats>> task = new Task<>() {
            @Override
            protected ObservableList<StaffRevenueStats> call() throws Exception {
                ArrayList<StaffRevenueStats> staffData =
                        RevenueDAO.fetchStaffRevenueDataFromDatabase(startDate, endDate);
                staffData.forEach(System.out::println);
                return FXCollections.observableArrayList(staffData);
            }

            @Override
            protected void succeeded() {
                staffRevenueTable.setItems(getValue());
                staffTableProgressBar.setVisible(false);
            }

            @Override
            protected void failed() {
                staffTableProgressBar.setVisible(false);
                getException().printStackTrace();
            }
        };

        new Thread(task).start();
    }

    private void updateRevenueLabels(Map<String, String> stats) {
        // Format revenue values with proper currency formatting
        if (totalRevenueValueLabel != null) {
            totalRevenueValueLabel.setText(formatCurrency(stats.get("totalRevenue")));
        }
        if (monthlyRevenueLabel != null) {
            monthlyRevenueLabel.setText(formatCurrency(stats.get("monthlyRevenue")));
        }
        if (lastMonthRevenueLabel != null) {
            lastMonthRevenueLabel.setText(formatCurrency(stats.get("lastMonthRevenue")));
        }
        if (revenueRatioLabel != null) {
            revenueRatioLabel.setText(formatPercentage(stats.get("revenueRatio")));
        }
    }

    private void updateStaffRevenueChart(ObservableList<XYChart.Data<String, Number>> data) {
        XYChart.Series<String, Number> series = new XYChart.Series<>();
        series.setName("Doanh thu nhân viên");
        series.getData().addAll(data);

        summaryChart.getData().clear();
        summaryChart.getData().add(series);

        // Add data labels to chart
        addDataLabelsToChart(series);
    }

    private void addDataLabelsToChart(XYChart.Series<String, Number> series) {
        Platform.runLater(() -> {
            Platform.runLater(() -> {
                for (XYChart.Data<String, Number> data : series.getData()) {
                    javafx.scene.Node node = data.getNode();
                    if (node != null) {
                        // Create label for the data value
                        Label dataLabel = new Label(formatCurrency(String.valueOf(data.getYValue().doubleValue())));
                        dataLabel.setStyle("-fx-font-size: 10px; -fx-text-fill: #333333; -fx-font-weight: bold; -fx-background-color: rgba(255,255,255,0.8); -fx-padding: 2px;");

                        // Add label to chart
                        if (node.getParent() instanceof javafx.scene.layout.Pane) {
                            javafx.scene.layout.Pane parent = (javafx.scene.layout.Pane) node.getParent();
                            parent.getChildren().add(dataLabel);

                            // Position the label above the bar
                            dataLabel.layoutBoundsProperty().addListener((obs, oldBounds, newBounds) -> {
                                double x = node.getBoundsInParent().getMinX() + node.getBoundsInParent().getWidth() / 2 - newBounds.getWidth() / 2;
                                double y = node.getBoundsInParent().getMinY() - newBounds.getHeight() - 5;
                                dataLabel.setLayoutX(x);
                                dataLabel.setLayoutY(y);
                            });
                        }
                    }
                }
            });
        });
    }

    private void updateStaffTable(ArrayList<StaffRevenueStats> data) {
        staffRevenueTable.setItems(FXCollections.observableArrayList(data));
    }

    private String formatCurrency(String value) {
        if (value == null || value.isEmpty()) return "0";
        try {
            double amount = Double.parseDouble(value.replace(",", ""));
            return String.format("%,.0f VND", amount);
        } catch (NumberFormatException e) {
            return value;
        }
    }

    private String formatPercentage(String value) {
        if (value == null || value.isEmpty()) return "0%";
        try {
            double percentage = Double.parseDouble(value.replace("%", ""));
            return String.format("%.1f%%", percentage);
        } catch (NumberFormatException e) {
            return value;
        }
    }
}
